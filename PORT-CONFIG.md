# Deep Research 端口配置指南

## 🔍 问题分析

### 原始问题
```bash
nohup env PORT=3001 pnpm start > app.log 2>&1 &
```
这个命令无法改变端口，因为：

1. **package.json硬编码端口**：`"start": "next start -p 3001"`
2. **命令行参数优先级更高**：`-p 3001`会覆盖环境变量`PORT=3001`
3. **端口冲突**：3001端口已被占用

## ✅ 解决方案

### 方案1：修改package.json（已实施）
```json
{
  "scripts": {
    "start": "next start -p ${PORT:-3001}"
  }
}
```

### 方案2：使用启动脚本（推荐）
```bash
# 给脚本执行权限
chmod +x start-server.sh

# 使用默认端口3001启动
./start-server.sh

# 使用指定端口启动
./start-server.sh 3002
```

### 方案3：手动命令
```bash
# 1. 检查端口占用
lsof -i :3001

# 2. 杀死占用进程（如果需要）
sudo kill -9 $(lsof -t -i:3001)

# 3. 使用新端口启动
export PORT=3002
pnpm start

# 或者直接指定端口
npx next start -p 3002
```

## 🛠️ 端口配置位置

### 1. 环境变量文件 (.env)
```bash
PORT=3002
```

### 2. package.json脚本
```json
{
  "scripts": {
    "dev": "next dev --turbopack -p ${PORT:-3001}",
    "start": "next start -p ${PORT:-3001}"
  }
}
```

### 3. 运行时环境变量
```bash
PORT=3002 pnpm start
```

### 4. 直接命令行参数
```bash
npx next start -p 3002
```

## 🔧 常用端口管理命令

### 查看端口占用
```bash
# 查看特定端口
lsof -i :3001
netstat -tulpn | grep :3001

# 查看所有Next.js进程
pgrep -f "next start"
ps aux | grep "next start"
```

### 停止服务
```bash
# 停止特定端口的进程
sudo kill -9 $(lsof -t -i:3001)

# 停止所有Next.js进程
pkill -f "next start"
```

### 启动服务
```bash
# 方法1：使用启动脚本
./start-server.sh 3002

# 方法2：环境变量
PORT=3002 pnpm start

# 方法3：直接命令
npx next start -p 3002

# 方法4：后台运行
nohup npx next start -p 3002 > app.log 2>&1 &
```

## 📋 部署检查清单

### 启动前检查
- [ ] 确认目标端口未被占用
- [ ] 检查.env文件中的PORT设置
- [ ] 确认package.json中的启动脚本
- [ ] 检查防火墙设置（如果需要外部访问）

### 启动后验证
- [ ] 检查进程是否正常运行：`ps aux | grep "next start"`
- [ ] 检查端口是否正确监听：`lsof -i :端口号`
- [ ] 检查日志是否有错误：`tail -f app.log`
- [ ] 测试访问：`curl http://localhost:端口号`

## 🚨 常见问题

### 1. 端口仍然是3001
**原因**：package.json中硬编码了端口
**解决**：使用修改后的package.json或直接用npx命令

### 2. 端口被占用
**原因**：其他进程正在使用该端口
**解决**：更换端口或停止占用进程

### 3. 权限问题
**原因**：使用1024以下的端口需要root权限
**解决**：使用1024以上的端口或sudo运行

### 4. 防火墙阻止
**原因**：服务器防火墙阻止了端口访问
**解决**：配置防火墙规则开放端口

## 🎯 推荐配置

### 开发环境
```bash
PORT=3001  # 默认开发端口
```

### 生产环境
```bash
PORT=3000  # 标准HTTP端口
# 或者
PORT=8080  # 常用应用端口
```

### 多实例部署
```bash
# 实例1
PORT=3001

# 实例2  
PORT=3002

# 实例3
PORT=3003
```

使用修改后的配置，现在您可以通过环境变量灵活控制端口了！
