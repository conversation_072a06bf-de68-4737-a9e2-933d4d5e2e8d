首先修改package.json文件，将端口改为3001
以及修改.env文件，将端口改为3001，以及api和key
pnpm build
nohup env PORT=3001 pnpm start > app.log 2>&1 &
或者port=3001 pnpm start

国内github镜像站
https://freevaults.com/github-mirror-daily-updates.html
# 直接访问类型镜像
git clone https://bgithub.xyz/jina-ai/node-DeepResearch.git
git clone https://kkgithub.com/jina-ai/node-DeepResearch.git
git clone https://github.ur1.fun/jina-ai/node-DeepResearch.git

# 对于 gitclone.com，需要加上路径
git clone https://gitclone.com/github.com/jina-ai/node-DeepResearch.git

如果git push出现问题，代理改一下
# 为Git配置代理
git config --global http.https://github.com.proxy http://127.0.0.1:7890
git config --global https.https://github.com.proxy http://127.0.0.1:7890

# 验证配置
git config --global --get http.https://github.com.proxy
git config --global --get https.https://github.com.proxy

ubuntu环境下
ss -tulpn | grep :3001 查看端口
lsof -i :3001

windows下
netstat -ano | findstr :3001
taskkill /PID 18032 /F