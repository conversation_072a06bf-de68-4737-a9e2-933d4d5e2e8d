@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;

    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;

    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;

    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;

    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;

    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;

    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;

    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;

    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;

    --input: 214.3 31.8% 91.4%;

    --ring: 222.2 84% 4.9%;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;

    --radius: 0.5rem;
  }
  .dark {
    --background: 222.2 84% 4.9%;

    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;

    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;

    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;

    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;

    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;

    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;

    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;

    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;

    --input: 217.2 32.6% 17.5%;

    --ring: 212.7 26.8% 83.9%;

    --chart-1: 220 70% 50%;

    --chart-2: 160 60% 45%;

    --chart-3: 30 80% 55%;

    --chart-4: 280 65% 60%;

    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@page {
  size: "A4";
}

.float-menu-button {
  @apply w-8 h-8 hover:bg-transparent text-slate-600 hover:text-slate-950 dark:text-slate-400 dark:hover:text-slate-50;
}

.hidden-empty-p p:empty {
  @apply hidden;
}

.from-item {
  @apply grid grid-cols-4 gap-2 max-sm:gap-1 space-y-0 items-center lg:grid-cols-6;
}
.from-label {
  @apply col-span-1 lg:col-span-2 lg:pl-1 overflow-hidden;
}
.form-field {
  @apply col-span-3 lg:col-span-4;
}

.text-line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
