'use client'
import dynamic from 'next/dynamic'
import { useLayoutEffect, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useTheme } from 'next-themes'
import { useGlobalStore } from '@/store/global'
import { useSettingStore } from '@/store/setting'
import { useTaskStore } from '@/store/task'

const Header = dynamic(() => import('@/components/Internal/Header'))
const Setting = dynamic(() => import('@/components/Setting'))
const Topic = dynamic(() => import('@/components/Research/Topic'))
const Feedback = dynamic(() => import('@/components/Research/Feedback'))
const SearchResult = dynamic(() => import('@/components/Research/SearchResult'))
const FinalReport = dynamic(() => import('@/components/Research/FinalReport'))
const History = dynamic(() => import('@/components/History'))
const Knowledge = dynamic(() => import('@/components/Knowledge'))
const HtmlPreview = dynamic(
  () => import('@/components/Research/FinalReport/HtmlPreview'),
  {
    ssr: false,
  }
)

function Home() {
  // const taskStore = useTaskStore()

  const { t } = useTranslation()
  const {
    openSetting,
    setOpenSetting,
    openHistory,
    setOpenHistory,
    openKnowledge,
    setOpenKnowledge,
  } = useGlobalStore()

  const { theme } = useSettingStore()
  const { setTheme } = useTheme()
  const htmlContent = useTaskStore((state) => state.htmlContent)

  useEffect(() => {
    if (htmlContent == '') {
      const style = document.createElement('style')
      style.innerHTML = `
      .main-box {
        margin: auto;
      }
      body{
        overflow-y:scroll;
      }
    `
      document.head.appendChild(style)
      return () => {
        document.head.removeChild(style)
      }
    }
  }, [htmlContent])

  useLayoutEffect(() => {
    const settingStore = useSettingStore.getState()
    setTheme(settingStore.theme)
  }, [theme, setTheme])

  return (
    <div className="flex">
      <div className="main-box max-lg:max-w-screen-md max-w-screen-lg px-4 mx-auto flex-1">
        <Header />
        <main>
          <Topic />
          <Feedback />
          <SearchResult />
          <FinalReport />
        </main>
        <footer className="my-4 text-center text-sm text-gray-600 print:hidden">
          <a href="https://github.com/u14app/" target="_blank">
            {t('copyright', {
              name: 'Deep Research',
            })}
          </a>
        </footer>
        <aside className="print:hidden">
          <Setting open={openSetting} onClose={() => setOpenSetting(false)} />
          <History open={openHistory} onClose={() => setOpenHistory(false)} />
          <Knowledge
            open={openKnowledge}
            onClose={() => setOpenKnowledge(false)}
          />
        </aside>
      </div>

      {htmlContent && <HtmlPreview />}
    </div>
  )
}

export default Home
