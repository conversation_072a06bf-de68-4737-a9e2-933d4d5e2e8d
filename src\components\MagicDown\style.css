.magicdown-editor {
  .cm-line {
    @apply leading-8 empty:my-0;
  }
  .cm-editor {
    @apply bg-transparent dark:text-slate-200;
    &.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground,
    .cm-selectionBackground,
    .cm-content ::selection {
      @apply bg-blue-400/40;
    }
  }
  .cm-tooltips {
    @apply text-slate-800 dark:text-slate-200 dark:bg-slate-800;
    & > span {
      @apply hover:bg-slate-200 hover:dark:bg-slate-700 p-0.5;
      & > svg {
        @apply w-5 h-5;
      }
    }
  }
  .cm-slash-command-list {
    @apply text-slate-800 dark:text-slate-200 dark:bg-slate-800;
    .cm-slash-command-item {
      @apply hover:bg-slate-200 hover:dark:bg-slate-700;
    }
    .cm-slash-label-item {
      @apply text-slate-500;
    }
  }
}

.magicdown-view {
  .hljs {
    background-color: transparent;
  }
  ul {
    @apply ps-4;
  }
  hr {
    @apply mt-6 mb-6;
  }
  .katex-display {
    overflow-y: auto;
  }
  a.reference {
    @apply text-xs before:content-["["] after:content-["]"];
  }
  .animate-fade-in {
    animation: fadeIn 0.2s ease-in;
  }
  .react-transform-wrapper {
    @apply w-auto h-auto;
  }
  &.mermaid-view {
    .react-transform-wrapper {
      @apply w-auto h-[50vh];
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
