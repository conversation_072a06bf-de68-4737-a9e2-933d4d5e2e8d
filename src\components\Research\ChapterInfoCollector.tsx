import React, { useState, useMemo } from 'react';
import { 
  Database, 
  Search, 
  ExternalLink, 
  FileText, 
  Clock,
  Filter,
  ChevronDown,
  ChevronUp,
  Download,
  Eye,
  Archive
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useChapterResearch } from '@/hooks/useChapterResearch';
import { useTaskStore } from '@/store/task';

interface ChapterInfoCollectorProps {
  maxHeight?: string;
}

export function ChapterInfoCollector({ 
  maxHeight = "600px" 
}: ChapterInfoCollectorProps) {
  const { chapterResearch } = useChapterResearch();
  const { tasks } = useTaskStore();
  const [selectedChapter, setSelectedChapter] = useState<string>('all');
  const [selectedRound, setSelectedRound] = useState<string>('all');
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [isMainCollapsed, setIsMainCollapsed] = useState(true); // 默认收缩

  // 获取章节相关的搜索任务
  const chapterTasks = useMemo(() => {
    return tasks.filter(task => 
      (task as any).taskType === 'chapter-research' && 
      task.state === 'completed'
    );
  }, [tasks]);

  // 过滤信息收集结果
  const filteredCollectedInfo = useMemo(() => {
    let info = Array.isArray(chapterResearch.collectedInfo) ? chapterResearch.collectedInfo : [];

    if (selectedChapter !== 'all') {
      info = info.filter(i => i.chapterId === selectedChapter);
    }

    if (selectedRound !== 'all') {
      const round = parseInt(selectedRound);
      info = info.filter(i => i.roundNumber === round);
    }

    return info.sort((a, b) => b.roundNumber - a.roundNumber);
  }, [chapterResearch.collectedInfo, selectedChapter, selectedRound]);

  // 获取章节标题
  const getChapterTitle = (chapterId: string) => {
    const chapters = Array.isArray(chapterResearch.chapters) ? chapterResearch.chapters : [];
    const chapter = chapters.find(c => c.id === chapterId);
    return chapter?.title || chapterId;
  };

  // 切换项目展开状态
  const toggleItem = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  // 格式化时间
  const formatTime = (timestamp: number) => {
    return new Date(timestamp || Date.now()).toLocaleString('zh-CN');
  };

  // 导出收集信息
  const exportCollectedInfo = () => {
    const data = {
      chapters: Array.isArray(chapterResearch.chapters) ? chapterResearch.chapters : [],
      collectedInfo: filteredCollectedInfo,
      exportTime: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { 
      type: 'application/json' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chapter-research-info-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 统计信息
  const stats = useMemo(() => {
    // 收集章节数 - 基于已选章节或全部章节
    const chaptersWithInfo = selectedChapter === 'all' 
      ? chapterResearch.chapters.filter(c => 
          chapterResearch.collectedInfo.some(info => info.chapterId === c.id)
        ).length
      : 1;
    
    // 收集总轮数 - 基于收集信息的轮数
    const totalRounds = filteredCollectedInfo.reduce((sum, info) => 
      Math.max(sum, info.roundNumber), 0
    );
    
    // 查询结果数
    const totalQueries = filteredCollectedInfo.reduce((sum, info) => 
      sum + info.searchResults.length, 0
    );
    
    return {
      totalChapters: chaptersWithInfo,
      totalRounds,
      totalQueries
    };
  }, [filteredCollectedInfo, chapterResearch.chapters, chapterResearch.collectedInfo, selectedChapter]);

  if (chapterResearch.collectedInfo.length === 0 && chapterTasks.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8 text-muted-foreground">
          <Database className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>尚无信息收集记录</p>
          <p className="text-sm mt-1">开始章节式研究后，收集的信息将在这里显示</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden">
      <Collapsible open={!isMainCollapsed} onOpenChange={() => setIsMainCollapsed(!isMainCollapsed)}>
        <CollapsibleTrigger asChild>
          <CardHeader className="pb-3 cursor-pointer hover:bg-muted/30 transition-colors">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Archive className="h-5 w-5 text-green-600" />
                信息收集记录
                <Badge variant="outline">{stats.totalQueries} 个查询</Badge>
                <Badge variant="secondary">{stats.totalChapters} 章 {stats.totalRounds} 轮</Badge>
              </CardTitle>
              
              <div className="flex items-center gap-2">
                {!isMainCollapsed && (
                  <>
                    <Button variant="outline" size="sm" onClick={(e) => {
                      e.stopPropagation();
                      exportCollectedInfo();
                    }}>
                      <Download className="h-4 w-4 mr-1" />
                      导出
                    </Button>
                    <Filter className="h-4 w-4 text-muted-foreground" />
                  </>
                )}
                {isMainCollapsed ? (
                  <ChevronDown className="h-5 w-5 text-muted-foreground" />
                ) : (
                  <ChevronUp className="h-5 w-5 text-muted-foreground" />
                )}
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <div className="px-4 pb-3 space-y-3">
            {/* 统计信息 */}
            <div className="grid grid-cols-3 gap-4 text-sm bg-muted/20 rounded-lg p-3">
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-600">{stats.totalChapters}</div>
                <div className="text-muted-foreground">收集章节</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-green-600">{stats.totalRounds}</div>
                <div className="text-muted-foreground">收集轮数</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-orange-600">{stats.totalQueries}</div>
                <div className="text-muted-foreground">查询结果</div>
              </div>
            </div>

            {/* 过滤器 */}
            <div className="flex gap-2 text-sm">
              <Select value={selectedChapter} onValueChange={setSelectedChapter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="选择章节" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有章节</SelectItem>
                  {Array.isArray(chapterResearch.chapters) && chapterResearch.chapters.map(chapter => (
                    <SelectItem key={chapter.id} value={chapter.id}>
                      {chapter.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={selectedRound} onValueChange={setSelectedRound}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="选择轮次" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有轮次</SelectItem>
                  {Array.from(new Set((Array.isArray(chapterResearch.collectedInfo) ? chapterResearch.collectedInfo : []).map(i => i.roundNumber)))
                    .sort((a, b) => a - b)
                    .map(round => (
                      <SelectItem key={round} value={round.toString()}>
                        第 {round} 轮
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <CardContent className="p-0">
            <ScrollArea style={{ height: maxHeight }}>
              <Tabs defaultValue="collected" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mx-4 mt-2 mb-4">
              <TabsTrigger value="collected">收集信息</TabsTrigger>
              <TabsTrigger value="tasks">搜索任务</TabsTrigger>
            </TabsList>
            
            {/* 收集信息标签页 */}
            <TabsContent value="collected" className="mt-0">
              <div className="px-4 pb-4 space-y-4">
                {filteredCollectedInfo.map((info) => {
                  const chapterTitle = getChapterTitle(info.chapterId);
                  const itemId = `collected-${info.chapterId}-${info.roundNumber}`;
                  const isExpanded = expandedItems.has(itemId);

                  return (
                    <Card key={itemId} className="border-l-4 border-l-blue-500">
                      <Collapsible
                        open={isExpanded}
                        onOpenChange={() => toggleItem(itemId)}
                      >
                        <CollapsibleTrigger asChild>
                          <CardHeader className="pb-2 cursor-pointer hover:bg-muted/50">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <div className="flex items-center gap-1 text-sm">
                                  <Database className="h-4 w-4 text-blue-600" />
                                  <span className="font-medium">{chapterTitle}</span>
                                  <Badge variant="secondary">第 {info.roundNumber} 轮</Badge>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline">
                                  {info.searchResults.length} 个结果
                                </Badge>
                                {isExpanded ? (
                                  <ChevronUp className="h-4 w-4" />
                                ) : (
                                  <ChevronDown className="h-4 w-4" />
                                )}
                              </div>
                            </div>
                            {info.summary && (
                              <p className="text-sm text-muted-foreground text-left mt-1">
                                {info.summary}
                              </p>
                            )}
                          </CardHeader>
                        </CollapsibleTrigger>

                        <CollapsibleContent>
                          <CardContent className="pt-0">
                            {info.searchResults.map((result, resultIndex) => (
                              <div 
                                key={resultIndex}
                                className="border rounded-lg p-3 mb-3 last:mb-0 bg-blue-50/30"
                              >
                                <div className="flex items-start justify-between gap-2 mb-2">
                                  <div className="flex items-center gap-2 min-w-0 flex-1">
                                    <Search className="h-4 w-4 text-blue-600 flex-shrink-0" />
                                    <span className="font-medium text-sm truncate">
                                      {result.query}
                                    </span>
                                  </div>
                                  <Badge variant="outline" className="text-xs">
                                    {result.sources?.length || 0} 来源
                                  </Badge>
                                </div>

                                {result.researchGoal && (
                                  <p className="text-xs text-muted-foreground mb-2">
                                    🎯 研究目标: {result.researchGoal}
                                  </p>
                                )}

                                <Collapsible>
                                  <CollapsibleTrigger asChild>
                                    <Button variant="ghost" size="sm" className="text-xs mb-2">
                                      <Eye className="h-3 w-3 mr-1" />
                                      查看结果内容
                                      <ChevronDown className="h-3 w-3 ml-1" />
                                    </Button>
                                  </CollapsibleTrigger>
                                  <CollapsibleContent>
                                    <div className="bg-white rounded p-3 text-sm max-h-40 overflow-y-auto">
                                      <p className="whitespace-pre-wrap leading-relaxed">
                                        {result.results || '暂无内容'}
                                      </p>
                                    </div>
                                  </CollapsibleContent>
                                </Collapsible>

                                {result.sources && result.sources.length > 0 && (
                                  <div className="mt-2">
                                    <h5 className="text-xs font-medium text-muted-foreground mb-1">
                                      信息来源:
                                    </h5>
                                    <div className="space-y-1">
                                      {result.sources.slice(0, 3).map((source, sourceIndex) => (
                                        <div key={sourceIndex} className="flex items-center gap-2 text-xs">
                                          <ExternalLink className="h-3 w-3 text-muted-foreground" />
                                          <a 
                                            href={source.url} 
                                            target="_blank" 
                                            rel="noopener noreferrer"
                                            className="text-blue-600 hover:underline truncate max-w-md"
                                          >
                                            {source.title || source.url}
                                          </a>
                                        </div>
                                      ))}
                                      {result.sources.length > 3 && (
                                        <p className="text-xs text-muted-foreground">
                                          还有 {result.sources.length - 3} 个来源...
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                )}
                              </div>
                            ))}
                          </CardContent>
                        </CollapsibleContent>
                      </Collapsible>
                    </Card>
                  );
                })}

                {filteredCollectedInfo.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Filter className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>没有符合条件的收集记录</p>
                    <p className="text-xs mt-1">尝试调整过滤条件</p>
                  </div>
                )}
              </div>
            </TabsContent>

            {/* 搜索任务标签页 */}
            <TabsContent value="tasks" className="mt-0">
              <div className="px-4 pb-4 space-y-3">
                {chapterTasks.map((task, taskIndex) => (
                  <Card key={task.query + taskIndex} className="border-l-4 border-l-green-500">
                    <CardContent className="p-3">
                      <div className="flex items-start justify-between gap-2 mb-2">
                        <div className="flex items-center gap-2 min-w-0 flex-1">
                          <FileText className="h-4 w-4 text-green-600 flex-shrink-0" />
                          <div className="min-w-0 flex-1">
                            <p className="font-medium text-sm truncate">{task.query}</p>
                            {(task as any).chapterId && (
                              <p className="text-xs text-muted-foreground">
                                章节: {getChapterTitle((task as any).chapterId)}
                                {(task as any).roundNumber && ` | 第 ${(task as any).roundNumber} 轮`}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <Badge 
                            variant={task.state === 'completed' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {task.state === 'completed' ? '已完成' : task.state}
                          </Badge>
                          {task.sources && (
                            <Badge variant="outline" className="text-xs">
                              {task.sources.length} 来源
                            </Badge>
                          )}
                        </div>
                      </div>

                      {task.researchGoal && (
                        <p className="text-xs text-muted-foreground mb-2">
                          🎯 {task.researchGoal}
                        </p>
                      )}

                      {(task as any).updatedAt && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>{formatTime((task as any).updatedAt)}</span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}

                {chapterTasks.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>暂无章节搜索任务</p>
                    <p className="text-xs mt-1">开启章节式研究后任务将在这里显示</p>
                  </div>
                )}
              </div>
            </TabsContent>
              </Tabs>
            </ScrollArea>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}