"use client";
import dynamic from "next/dynamic";
import { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import {
  LoaderCircle,
  CircleCheck,
  TextSearch,
  Download,
  Trash,
  RotateCcw,
  NotebookText,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { Button } from "@/components/Internal/Button";
import SearchModeToggle from "@/components/Internal/SearchModeToggle";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import useAccurateTimer from "@/hooks/useAccurateTimer";
import useDeepResearch from "@/hooks/useDeepResearch";
import useKnowledge from "@/hooks/useKnowledge";
import { useTaskStore } from "@/store/task";
import { useKnowledgeStore } from "@/store/knowledge";
// import { useSettingStore } from "@/store/setting"; // 暂时不需要，但保留以备将来使用
import { downloadFile } from "@/utils/file";
// 简单的轮次标签格式化函数
function formatRoundLabel(roundNumber: number): string {
  return roundNumber ? `[第${roundNumber}轮]` : "";
}

const MagicDown = dynamic(() => import("@/components/MagicDown"));
const MagicDownView = dynamic(() => import("@/components/MagicDown/View"));
const Lightbox = dynamic(() => import("@/components/Internal/Lightbox"));

const formSchema = z.object({
  suggestion: z.string().optional(),
});

function addQuoteBeforeAllLine(text: string = "") {
  return text
    .split("\n")
    .map((line) => `> ${line}`)
    .join("\n");
}

function TaskState({ state }: { state: SearchTask["state"] }) {
  if (state === "completed") {
    return <CircleCheck className="h-5 w-5" />;
  } else if (state === "processing") {
    return <LoaderCircle className="animate-spin h-5 w-5" />;
  } else {
    return <TextSearch className="h-5 w-5" />;
  }
}

function SearchResult() {
  const { t } = useTranslation();
  const taskStore = useTaskStore();
  // 注意：searchMode 在这里暂时不需要使用，但保留以备将来扩展
  // const { searchMode } = useSettingStore();
  const { status, runSearchTask, reviewSearchResult } = useDeepResearch();
  const { generateId } = useKnowledge();
  const {
    formattedTime,
    start: accurateTimerStart,
    stop: accurateTimerStop,
  } = useAccurateTimer();
  const [isThinking, setIsThinking] = useState<boolean>(false);
  const [isCollapsed, setIsCollapsed] = useState<boolean>(true); // 默认收缩
  const unfinishedTasks = useMemo(() => {
    return taskStore.tasks.filter((item) => item.state !== "completed");
  }, [taskStore.tasks]);
  const taskFinished = useMemo(() => {
    return taskStore.tasks.length > 0 && unfinishedTasks.length === 0;
  }, [taskStore.tasks, unfinishedTasks]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      suggestion: taskStore.suggestion,
    },
  });

  // 获取任务的显示查询词（现在直接使用task.query）
  function getDisplayQuery(item: SearchTask) {
    return item.query;
  }

  // 获取任务的语言标识
  function getLanguageLabel(item: SearchTask) {
    return item.language === "chinese" ? "中文" : "English";
  }

  // 获取任务的轮次和类型标识
  function getRoundAndTypeLabel(item: SearchTask) {
    const roundLabel = item.roundNumber ? formatRoundLabel(item.roundNumber) : "";
    const languageLabel = getLanguageLabel(item);

    if (roundLabel) {
      return `${roundLabel} (${languageLabel})`;
    }
    return `(${languageLabel})`;
  }

  // 检查是否为特殊任务（非搜索任务）
  function isSpecialTask(item: SearchTask) {
    return item.taskType === 'reflection' ||
           item.taskType === 'evaluation' ||
           item.taskType === 'chapter_start' ||
           item.taskType === 'agent_discussion' ||
           item.taskType === 'chapter_completed';
  }

  // 获取任务类型的显示名称
  function getTaskTypeName() {
    return t("research.autoResearch.search", "搜索查询"); // 简化版本，统一显示为搜索任务
  }

  function getSearchResultContent(item: SearchTask) {
    const roundInfo = item.roundNumber ? `${formatRoundLabel(item.roundNumber)} ` : "";
    const taskTypeInfo = item.taskType ? ` - ${getTaskTypeName()}` : "";

    return [
      `## ${roundInfo}${getDisplayQuery(item)}${taskTypeInfo}`,
      addQuoteBeforeAllLine(item.researchGoal),
      "---",
      item.learning,
      item.images?.length > 0
        ? `#### ${t("research.searchResult.relatedImages")}\n\n${item.images
            .map(
              (source) =>
                `![${source.description || source.url}](${source.url})`
            )
            .join("\n")}`
        : "",
      item.sources?.length > 0
        ? `#### ${t("research.common.sources")}\n\n${item.sources
            .map(
              (source, idx) =>
                `${idx + 1}. [${source.title || source.url}][${idx + 1}]`
            )
            .join("\n")}`
        : "",
    ].join("\n\n");
  }

  async function handleSubmit(values: z.infer<typeof formSchema>) {
    const { setSuggestion } = useTaskStore.getState();
    try {
      accurateTimerStart();
      setIsThinking(true);
      if (unfinishedTasks.length > 0) {
        await runSearchTask(unfinishedTasks);
      } else {
        if (values.suggestion) setSuggestion(values.suggestion);
        await reviewSearchResult();
        // Clear previous research suggestions
        setSuggestion("");
      }
    } finally {
      setIsThinking(false);
      accurateTimerStop();
    }
  }

  function addToKnowledgeBase(item: SearchTask) {
    const { save } = useKnowledgeStore.getState();
    const currentTime = Date.now();
    save({
      id: generateId("knowledge"),
      title: getDisplayQuery(item),
      content: getSearchResultContent(item),
      type: "knowledge",
      createdAt: currentTime,
      updatedAt: currentTime,
    });
    toast.message(t("research.common.addToKnowledgeBaseTip"));
  }

  async function handleRetry(query: string, language: "chinese" | "english", researchGoal: string, groupId?: string) {
    const { updateTask } = useTaskStore.getState();
    const newTask: SearchTask = {
      query,
      language,
      groupId,
      researchGoal,
      learning: "",
      sources: [],
      images: [],
      state: "unprocessed",
    };
    updateTask(query, newTask);
    await runSearchTask([newTask]);
  }

  function handleRemove(query: string, language: "chinese" | "english") {
    const { removeTask, canDeleteTask } = useTaskStore.getState();

    // 检查是否可以安全删除
    if (!canDeleteTask(query, language)) {
      toast.warning(t("research.common.cannotDeleteProcessingTask", "无法删除正在处理中的任务，请等待搜索完成"));
      return;
    }

    removeTask(query, language);
    toast.success(t("research.common.taskDeleted", "任务已删除"));
  }

  useEffect(() => {
    form.setValue("suggestion", taskStore.suggestion);
  }, [taskStore.suggestion, form]);

  return (
    <>

      <section className="p-4 border rounded-md mt-4 print:hidden">
        <Collapsible open={!isCollapsed} onOpenChange={() => setIsCollapsed(!isCollapsed)}>
          <CollapsibleTrigger asChild>
            <div className="flex justify-between items-center border-b mb-2 cursor-pointer hover:bg-muted/20 transition-colors px-2 -mx-2 rounded">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-lg leading-10">
                  {t("research.searchResult.title")}
                </h3>
                {taskStore.tasks.length > 0 && (
                  <span className="text-sm text-muted-foreground">
                    ({taskStore.tasks.length} 项)
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
                {!isCollapsed && <SearchModeToggle />}
                {isCollapsed ? (
                  <ChevronDown className="h-5 w-5 text-muted-foreground" />
                ) : (
                  <ChevronUp className="h-5 w-5 text-muted-foreground" />
                )}
              </div>
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            {taskStore.tasks.length === 0 ? (
              <div>{t("research.searchResult.emptyTip")}</div>
            ) : (
              <div>
          {/* 调试日志 */}
          {(() => {
            const allTasks = taskStore.tasks;
            // 简化版本，移除过滤和复杂的调试日志
            console.log('🔍 [SearchResult] 显示任务数量:', allTasks.length);
            
            return null;
          })()}
          
          <Accordion className="mb-4" type="multiple">
            {taskStore.tasks
              .map((item, idx) => { // 简化版本，显示所有搜索任务
              return (
                <AccordionItem key={idx} value={item.query}>
                  <AccordionTrigger>
                    <div className="flex items-center">
                      <TaskState state={item.state} />
                      <span className="ml-1">{getDisplayQuery(item)}</span>
                      {/* 显示轮次和语言标识 */}
                      <span className="ml-2 text-xs text-muted-foreground">
                        {getRoundAndTypeLabel(item)}
                      </span>
                      {/* 特殊任务类型标识 */}
                      {isSpecialTask(item) && (
                        <span className="ml-2 px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                          {getTaskTypeName()}
                        </span>
                      )}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="prose prose-slate dark:prose-invert max-w-full min-h-20">
                    <MagicDownView>
                      {addQuoteBeforeAllLine(item.researchGoal)}
                    </MagicDownView>
                    <Separator className="mb-4" />
                    <MagicDown
                      value={item.learning}
                      onChange={(value) =>
                        taskStore.updateTask(item.query, { learning: value })
                      }
                      tools={
                        <>
                          <div className="px-1">
                            <Separator className="dark:bg-slate-700" />
                          </div>
                          {/* 只有搜索任务才显示重试和删除按钮 */}
                          {!isSpecialTask(item) && (
                            <>
                              <Button
                                className="float-menu-button"
                                type="button"
                                size="icon"
                                variant="ghost"
                                title={t("research.common.restudy")}
                                side="left"
                                sideoffset={8}
                                onClick={() =>
                                  handleRetry(item.query, item.language, item.researchGoal, item.groupId)
                                }
                              >
                                <RotateCcw />
                              </Button>
                              <Button
                                className="float-menu-button"
                                type="button"
                                size="icon"
                                variant="ghost"
                                title={t("research.common.delete")}
                                side="left"
                                sideoffset={8}
                                onClick={() => handleRemove(item.query, item.language)}
                              >
                                <Trash />
                              </Button>
                              <div className="px-1">
                                <Separator className="dark:bg-slate-700" />
                              </div>
                            </>
                          )}
                          <Button
                            className="float-menu-button"
                            type="button"
                            size="icon"
                            variant="ghost"
                            title={t("research.common.addToKnowledgeBase")}
                            side="left"
                            sideoffset={8}
                            onClick={() => addToKnowledgeBase(item)}
                          >
                            <NotebookText />
                          </Button>
                          <Button
                            className="float-menu-button"
                            type="button"
                            size="icon"
                            variant="ghost"
                            title={t("research.common.export")}
                            side="left"
                            sideoffset={8}
                            onClick={() =>
                              downloadFile(
                                getSearchResultContent(item),
                                `${item.query}.md`,
                                "text/markdown;charset=utf-8"
                              )
                            }
                          >
                            <Download />
                          </Button>
                        </>
                      }
                    ></MagicDown>
                    {item.images?.length > 0 ? (
                      <>
                        <hr className="my-6" />
                        <h4>{t("research.searchResult.relatedImages")}</h4>
                        <Lightbox data={item.images}></Lightbox>
                      </>
                    ) : null}
                    {item.sources?.length > 0 ? (
                      <>
                        <hr className="my-6" />
                        <h4>{t("research.common.sources")}</h4>
                        <ol>
                          {item.sources.map((source, idx) => {
                            return (
                              <li className="ml-2" key={idx}>
                                <a href={source.url} target="_blank">
                                  {source.title || source.url}
                                </a>
                              </li>
                            );
                          })}
                        </ol>
                      </>
                    ) : null}
                  </AccordionContent>
                </AccordionItem>
              );
            })}
          </Accordion>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)}>
              <FormField
                control={form.control}
                name="suggestion"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="mb-2 font-semibold">
                      {t("research.searchResult.suggestionLabel")}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        rows={3}
                        placeholder={t(
                          "research.searchResult.suggestionPlaceholder"
                        )}
                        disabled={isThinking}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <Button
                className="w-full mt-4"
                type="submit"
                variant="default"
                disabled={isThinking}
              >
                {isThinking ? (
                  <>
                    <LoaderCircle className="animate-spin" />
                    <span>{status}</span>
                    <small className="font-mono">{formattedTime}</small>
                  </>
                ) : taskFinished ? (
                  t("research.common.indepthResearch")
                ) : (
                  t("research.common.continueResearch")
                )}
              </Button>
            </form>
          </Form>
        </div>
      )}
          </CollapsibleContent>
        </Collapsible>
      </section>
    </>
  );
}

export default SearchResult;
