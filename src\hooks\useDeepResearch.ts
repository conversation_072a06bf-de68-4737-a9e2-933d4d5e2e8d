// React hooks 和状态管理相关导入
import { useState } from "react";
// AI SDK 相关导入，用于流式文本生成和工具调用
import { streamText, smoothStream, type JSONValue, type Tool } from "ai";
import { parsePartialJson } from "@ai-sdk/ui-utils";
import { openai } from "@ai-sdk/openai";
import { type GoogleGenerativeAIProviderMetadata } from "@ai-sdk/google";
// 国际化支持
import { useTranslation } from "react-i18next";
// 并发控制库，用于限制同时执行的任务数量
import Plimit from "p-limit";
// 消息提示组件
import { toast } from "sonner";
// 自定义 hooks 导入
import useModelProvider from "@/hooks/useAiProvider";
import useWebSearch from "@/hooks/useWebSearch";
// 状态管理 stores
import { useTaskStore, type AdversarialRound } from "@/store/task";
import { useHistoryStore } from "@/store/history";
import { useSettingStore } from "@/store/setting";
import { useKnowledgeStore } from "@/store/knowledge";
// 提示词常量
import { outputGuidelinesPrompt } from "@/constants/prompts";
// 深度研究相关的提示词和工具函数
import {
  getSystemPrompt,
  generateQuestionsPrompt,
  writeReportPlanPrompt,
  generateSerpQueriesPrompt,
  processResultPrompt,
  processSearchResultPrompt,
  processSearchKnowledgeResultPrompt,
  reviewSerpQueriesPrompt,
  getReportPlanPrompt,
  getSERPQuerySchema,
} from "@/utils/deep-research/prompts";
// 报告格式相关工具函数
import { buildFinalReportPrompt } from "@/utils/deep-research/report-formats";
// 模型相关工具函数
import { isNetworkingModel } from "@/utils/model";
// 文本处理工具
import { ThinkTagStreamProcessor, removeJsonMarkdown } from "@/utils/text";
// 错误处理工具
import { parseError } from "@/utils/error";
// 数组处理工具函数
import { pick, flat, unique } from "radash";

// 类型定义：提供商选项配置
type ProviderOptions = Record<string, Record<string, JSONValue>>;
// 类型定义：工具集合
type Tools = Record<string, Tool>;

/**
 * 获取响应语言提示词
 * 确保AI使用与用户相同的语言进行回复
 */
function getResponseLanguagePrompt() {
  return `\n\n**Respond in the same language as the user's language**`;
}

/**
 * 创建平滑文本流
 * @param type 流式输出类型：字符、单词或行
 * @returns 配置好的平滑流对象
 */
function smoothTextStream(type: "character" | "word" | "line") {
  return smoothStream({
    chunking: type === "character" ? /./ : type,
    delayInMs: 0,
  });
}

/**
 * 统一错误处理函数
 * 解析错误信息并显示toast提示
 */
function handleError(error: unknown) {
  const errorMessage = parseError(error);
  toast.error(errorMessage);
}

/**
 * 深度研究主要Hook
 * 提供深度研究的核心功能，包括问题生成、报告规划、搜索执行、结果审查和最终报告生成
 */
function useDeepResearch() {
  // 国际化翻译函数
  const { t } = useTranslation();
  // 任务状态管理
  const taskStore = useTaskStore();
  // 获取平滑文本流类型设置
  const { smoothTextStreamType } = useSettingStore();
  // 模型提供商相关函数
  const { createModelProvider, getModel } = useModelProvider();
  // 网络搜索功能
  const { search } = useWebSearch();
  // 当前状态（用于显示进度）
  const [status, setStatus] = useState<string>("");

  /**
   * 生成研究问题
   * 基于用户输入的问题，使用AI生成更详细的研究问题列表
   */
  async function askQuestions() {
    // 获取用户输入的原始问题
    const { question } = useTaskStore.getState();
    // 获取思考模型配置
    const { thinkingModel } = getModel();
    // 设置状态为"思考中"
    setStatus(t("research.common.thinking"));
    // 创建思考标签流处理器，用于处理AI的思考过程
    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
    // 调用AI生成问题
    const result = streamText({
      model: await createModelProvider(thinkingModel),
      system: getSystemPrompt(),
      prompt: [
        generateQuestionsPrompt(question),
        getResponseLanguagePrompt(),
      ].join("\n\n"),
      experimental_transform: smoothTextStream(smoothTextStreamType),
      onError: handleError,
    });
    let content = "";  // 存储生成的内容
    let reasoning = ""; // 存储AI的推理过程
    taskStore.setQuestion(question);
    // 处理流式响应
    for await (const part of result.fullStream) {
      if (part.type === "text-delta") {
        // 处理文本增量，分离内容和推理过程
        thinkTagStreamProcessor.processChunk(
          part.textDelta,
          (data) => {
            content += data;
            taskStore.updateQuestions(content);
          },
          (data) => {
            reasoning += data;
          }
        );
      } else if (part.type === "reasoning") {
        // 处理推理类型的响应
        reasoning += part.textDelta;
      }
    }
    // 如果有推理过程，输出到控制台（用于调试）
    if (reasoning) console.log(reasoning);
  }

  /**
   * 编写报告计划
   * 基于研究查询生成详细的报告结构和计划
   */
  async function writeReportPlan() {
    // 获取研究查询
    const { query } = useTaskStore.getState();
    // 获取思考模型
    const { thinkingModel } = getModel();
    // 设置状态为思考中
    setStatus(t("research.common.thinking"));
    // 创建思考标签流处理器
    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
    // 调用AI生成报告计划
    const result = streamText({
      model: await createModelProvider(thinkingModel),
      system: getSystemPrompt(),
      prompt: [writeReportPlanPrompt(query), getResponseLanguagePrompt()].join(
        "\n\n"
      ),
      experimental_transform: smoothTextStream(smoothTextStreamType),
      onError: handleError,
    });
    let content = "";  // 存储生成的报告计划内容
    let reasoning = ""; // 存储AI推理过程
    // 处理流式响应
    for await (const part of result.fullStream) {
      if (part.type === "text-delta") {
        thinkTagStreamProcessor.processChunk(
          part.textDelta,
          (data) => {
            content += data;
            taskStore.updateReportPlan(content);
          },
          (data) => {
            reasoning += data;
          }
        );
      } else if (part.type === "reasoning") {
        reasoning += part.textDelta;
      }
    }
    if (reasoning) console.log(reasoning);
    
    // 检测并转换JSON格式的计划（和对抗性计划保持一致）
    try {
      const cleanedContent = removeJsonMarkdown(content);
      const parsedContent = JSON.parse(cleanedContent);
      
      // 检查是否有sections数组（单步生成的JSON格式）
      if (parsedContent.sections && Array.isArray(parsedContent.sections)) {
        console.log('🔄 [计划转换] 检测到单步JSON格式计划，转换为Markdown格式');
        
        // 转换sections数组为Markdown格式
        const markdownPlan = parsedContent.sections
          .map((item: {section_title: string; summary: string}) => 
            `## ${item.section_title}\n${item.summary}`)
          .join('\n\n');
        
        console.log('✅ [计划转换] JSON转Markdown成功，章节数:', parsedContent.sections.length);
        
        // 更新最终的Markdown格式内容
        content = markdownPlan;
        taskStore.updateReportPlan(content);
      }
    } catch {
      // JSON解析失败，保持原内容不变（向后兼容）
      console.log('📝 [计划格式] 内容不是JSON格式或解析失败，保持原格式');
    }
    
    return content;
  }

  /**
   * 搜索本地知识库
   * 在已上传的本地资源中搜索相关信息
   * @param query 搜索查询
   * @param researchGoal 研究目标
   * @returns 搜索结果内容
   */
  async function searchLocalKnowledges(query: string, researchGoal: string) {
    // 获取已上传的资源列表
    const { resources } = useTaskStore.getState();
    const knowledgeStore = useKnowledgeStore.getState();
    const knowledges: Knowledge[] = [];

    // 遍历资源，收集已完成处理的知识库内容
    for (const item of resources) {
      if (item.status === "completed") {
        const resource = knowledgeStore.get(item.id);
        if (resource) {
          knowledges.push(resource);
        }
      }
    }

    // 获取网络模型配置
    const { networkingModel } = getModel();
    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
    // 使用AI处理本地知识库搜索结果
    const searchResult = streamText({
      model: await createModelProvider(networkingModel),
      system: getSystemPrompt(),
      prompt: [
        processSearchKnowledgeResultPrompt(query, researchGoal, knowledges),
        getResponseLanguagePrompt(),
      ].join("\n\n"),
      experimental_transform: smoothTextStream(smoothTextStreamType),
      onError: handleError,
    });
    let content = "";  // 存储处理后的内容
    let reasoning = ""; // 存储推理过程
    // 处理流式响应
    for await (const part of searchResult.fullStream) {
      if (part.type === "text-delta") {
        thinkTagStreamProcessor.processChunk(
          part.textDelta,
          (data) => {
            content += data;
            taskStore.updateTask(query, { learning: content });
          },
          (data) => {
            reasoning += data;
          }
        );
      } else if (part.type === "reasoning") {
        reasoning += part.textDelta;
      }
    }
    if (reasoning) console.log(reasoning);
    return content;
  }

  /**
   * 执行搜索任务
   * 并行执行多个搜索查询，获取相关信息并处理结果
   * @param queries 搜索任务列表
   */
  async function runSearchTask(queries: SearchTask[]) {
    // 获取设置配置
    const {
      provider,        // AI提供商
      enableSearch,    // 是否启用搜索
      searchProvider,  // 搜索提供商
      parallelSearch,  // 并行搜索数量
      searchMaxResult, // 最大搜索结果数
      references,      // 是否启用引用

    } = useSettingStore.getState();
    // 获取本地资源
    const { resources } = useTaskStore.getState();
    // 获取网络模型
    const { networkingModel } = getModel();
    // 设置状态为研究中
    setStatus(t("research.common.research"));
    // 创建并发限制器，控制同时执行的搜索任务数量
    const plimit = Plimit(parallelSearch);
    // 创建思考标签流处理器
    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();

    /**
     * 获取实际搜索使用的查询词（现在直接使用task.query）
     */
    const getSearchQuery = (item: SearchTask) => {
      return item.query; // 直接使用查询词，因为现在每个任务都有明确的语言标识
    };

    /**
     * 创建模型实例
     * 根据配置决定是否启用内置搜索功能
     */
    const createModel = (model: string) => {
      // 为Google Gemini启用内置搜索工具
      if (
        enableSearch &&
        searchProvider === "model" &&
        provider === "google" &&
        isNetworkingModel(model)
      ) {
        return createModelProvider(model, { useSearchGrounding: true });
      } else {
        return createModelProvider(model);
      }
    };

    /**
     * 获取工具配置
     * 为支持的模型启用搜索工具
     */
    const getTools = (model: string) => {
      // 为OpenAI启用内置搜索工具
      if (enableSearch && searchProvider === "model") {
        if (
          ["openai", "azure"].includes(provider) &&
          model.startsWith("gpt-4o")
        ) {
          return {
            web_search_preview: openai.tools.webSearchPreview({
              // 可选配置：搜索上下文大小
              searchContextSize: "medium",
            }),
          } as Tools;
        }
      }
      return undefined;
    };

    /**
     * 获取提供商特定选项
     * 为不同的AI提供商配置搜索参数
     */
    const getProviderOptions = (model: string) => {
      if (enableSearch && searchProvider === "model") {
        // 为OpenRouter启用内置搜索工具
        if (provider === "openrouter") {
          return {
            openrouter: {
              plugins: [
                {
                  id: "web",
                  max_results: searchMaxResult, // 默认为5
                },
              ],
            },
          } as ProviderOptions;
        } else if (
          provider === "xai" &&
          model.startsWith("grok-3") &&
          !model.includes("mini")
        ) {
          // 为xAI Grok配置搜索参数
          return {
            xai: {
              search_parameters: {
                mode: "auto",
                max_search_results: searchMaxResult,
              },
            },
          } as ProviderOptions;
        }
      }
      return undefined;
    };
    // 并行执行所有搜索任务
    await Promise.all(
      queries.map((item) => {
        // 使用并发限制器控制同时执行的任务数量
        plimit(async () => {
          let content = "";           // 存储搜索结果内容
          let reasoning = "";         // 存储AI推理过程
          let searchResult;          // 搜索结果流
          let sources: Source[] = []; // 搜索来源列表
          let images: ImageSource[] = []; // 图片来源列表

          // 更新任务状态为处理中
          taskStore.updateTask(item.query, { state: "processing" });

          // 如果有本地资源，先搜索本地知识库
          if (resources.length > 0) {
            const knowledges = await searchLocalKnowledges(
              item.query,
              item.researchGoal
            );
            // 将本地知识库结果添加到内容中
            content += [
              knowledges,
              `### ${t("research.searchResult.references")}`,
              resources.map((item) => `- ${item.name}`).join("\n"),
              "---",
              "",
            ].join("\n\n");
          }

          // 如果启用了搜索功能
          if (enableSearch) {
            // 使用外部搜索提供商（非模型内置搜索）
            if (searchProvider !== "model") {
              try {
                // 执行网络搜索，使用用户选择的语言查询词
                const searchQuery = getSearchQuery(item);
                const results = await search(searchQuery);
                sources = results.sources;
                images = results.images;

                // 检查搜索结果是否有效
                if (sources.length === 0) {
                  throw new Error("Invalid Search Results");
                }
              } catch (err) {
                console.error(err);
                handleError(
                  `[${searchProvider}]: ${
                    err instanceof Error ? err.message : "Search Failed"
                  }`
                );
                // 搜索失败时清空队列
                return plimit.clearQueue();
              }
              // 判断是否启用引用功能
              const enableReferences =
                sources.length > 0 && references === "enable";
              // 使用AI处理搜索结果
              searchResult = streamText({
                model: await createModel(networkingModel),
                system: getSystemPrompt(),
                prompt: [
                  processSearchResultPrompt(
                    getSearchQuery(item),
                    item.researchGoal,
                    sources,
                    enableReferences
                  ),
                  getResponseLanguagePrompt(),
                ].join("\n\n"),
                experimental_transform: smoothTextStream(smoothTextStreamType),
                onError: handleError,
              });
            } else {
              // 使用模型内置搜索功能
              searchResult = streamText({
                model: await createModel(networkingModel),
                system: getSystemPrompt(),
                prompt: [
                  processResultPrompt(getSearchQuery(item), item.researchGoal),
                  getResponseLanguagePrompt(),
                ].join("\n\n"),
                tools: getTools(networkingModel),
                providerOptions: getProviderOptions(networkingModel),
                experimental_transform: smoothTextStream(smoothTextStreamType),
                onError: handleError,
              });
            }
          } else {
            // 未启用搜索时，直接使用AI处理查询
            searchResult = streamText({
              model: await createModelProvider(networkingModel),
              system: getSystemPrompt(),
              prompt: [
                processResultPrompt(getSearchQuery(item), item.researchGoal),
                getResponseLanguagePrompt(),
              ].join("\n\n"),
              experimental_transform: smoothTextStream(smoothTextStreamType),
              onError: (err) => {
                taskStore.updateTask(item.query, { state: "failed" });
                handleError(err);
              },
            });
          }
          // 处理搜索结果的流式响应
          for await (const part of searchResult.fullStream) {
            if (part.type === "text-delta") {
              // 处理文本增量，分离内容和推理过程
              thinkTagStreamProcessor.processChunk(
                part.textDelta,
                (data) => {
                  content += data;
                  taskStore.updateTask(item.query, { learning: content });
                },
                (data) => {
                  reasoning += data;
                }
              );
            } else if (part.type === "reasoning") {
              // 处理推理类型的响应
              reasoning += part.textDelta;
            } else if (part.type === "source") {
              // 收集搜索来源信息
              sources.push(part.source);
            } else if (part.type === "finish") {
              // 处理完成时的元数据
              if (part.providerMetadata?.google) {
                // 处理Google Gemini的grounding元数据
                const { groundingMetadata } = part.providerMetadata.google;
                const googleGroundingMetadata =
                  groundingMetadata as GoogleGenerativeAIProviderMetadata["groundingMetadata"];
                if (googleGroundingMetadata?.groundingSupports) {
                  // 为grounding支持的文本段添加引用索引
                  googleGroundingMetadata.groundingSupports.forEach(
                    ({ segment, groundingChunkIndices }) => {
                      if (segment.text && groundingChunkIndices) {
                        const index = groundingChunkIndices.map(
                          (idx: number) => `[${idx + 1}]`
                        );
                        content = content.replaceAll(
                          segment.text,
                          `${segment.text}${index.join("")}`
                        );
                      }
                    }
                  );
                }
              } else if (part.providerMetadata?.openai) {
                // 修复OpenAI在中文环境下无法正确生成markdown引用链接语法的问题
                content = content.replaceAll("【", "[").replaceAll("】", "]");
              }
            }
          }
          // 如果有推理过程，输出到控制台（用于调试）
          if (reasoning) console.log(reasoning);

          // 如果有搜索来源，添加引用链接
          if (sources.length > 0) {
            content +=
              "\n\n" +
              sources
                .map(
                  (item, idx) =>
                    `[${idx + 1}]: ${item.url}${
                      item.title ? ` "${item.title.replaceAll('"', " ")}"` : ""
                    }`
                )
                .join("\n");
          }

          // 根据内容长度更新任务状态
          if (content.length > 0) {
            // 任务成功完成
            taskStore.updateTask(item.query, {
              state: "completed",
              learning: content,
              sources,
              images,
            });
            return content;
          } else {
            // 任务失败
            taskStore.updateTask(item.query, {
              state: "failed",
              learning: "",
              sources: [],
              images: [],
            });
            return "";
          }
        });
      })
    );
  }

  /**
   * 审查搜索结果
   * 基于已有的搜索结果和报告计划，生成额外的搜索查询来补充信息
   */
  async function reviewSearchResult() {
    // 获取报告计划、已完成的任务和用户建议
    const { reportPlan, tasks, suggestion } = useTaskStore.getState();
    const { searchMode } = useSettingStore.getState();
    const { thinkingModel } = getModel();
    setStatus(t("research.common.research"));
    // 提取所有任务的学习内容
    const learnings = tasks.map((item) => item.learning);
    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
    // 使用AI审查现有结果并生成新的搜索查询
    const result = streamText({
      model: await createModelProvider(thinkingModel),
      system: getSystemPrompt(),
      prompt: [
        reviewSerpQueriesPrompt(reportPlan, learnings, suggestion, searchMode),
        getResponseLanguagePrompt(),
      ].join("\n\n"),
      experimental_transform: smoothTextStream(smoothTextStreamType),
      onError: handleError,
    });

    // 获取搜索查询的JSON模式验证器
    const querySchema = getSERPQuerySchema();
    let content = "";           // 存储AI生成的内容
    let reasoning = "";         // 存储推理过程
    let queries: SearchTask[] = []; // 存储解析出的新查询

    // 处理流式文本响应
    for await (const textPart of result.textStream) {
      thinkTagStreamProcessor.processChunk(
        textPart,
        (text) => {
          content += text;
          // 尝试解析JSON格式的查询列表
          const data: PartialJson = parsePartialJson(
            removeJsonMarkdown(content)
          );
          // 验证解析结果是否符合查询模式
          if (
            querySchema.safeParse(data.value) &&
            data.state === "successful-parse"
          ) {
            if (data.value) {
              // 将解析出的查询转换为SearchTask格式
              queries = data.value.map(
                (item: { query: string; language: "chinese" | "english"; groupId?: string; researchGoal: string }) => ({
                  state: "unprocessed",
                  learning: "",
                  sources: [],
                  images: [],
                  ...pick(item, ["query", "language", "groupId", "researchGoal"]),
                })
              );
            }
          }
        },
        (text) => {
          reasoning += text;
        }
      );
    }
    if (reasoning) console.log(reasoning);
    // 如果生成了新的查询，添加到任务列表并执行搜索
    if (queries.length > 0) {
      taskStore.update([...tasks, ...queries]);
      await runSearchTask(queries);
    }
  }

  /**
   * 编写最终报告
   * 基于所有搜索结果和报告计划，生成完整的研究报告
   */
  async function writeFinalReport() {
    // 获取设置配置
    const { citationImage, references } = useSettingStore.getState();
    // 获取任务相关状态和函数
    const {
      reportPlan,        // 报告计划
      tasks,            // 所有任务
      setId,            // 设置报告ID
      setTitle,         // 设置报告标题
      setSources,       // 设置引用来源
      requirement,      // 用户需求
      updateFinalReport, // 更新最终报告内容
      reportType,       // 报告类型
      customPrompt,     // 自定义prompt
      customFormats,    // 自定义格式列表
      selectedCustomFormatId, // 选中的自定义格式ID
    } = useTaskStore.getState();
    // 获取历史记录保存函数
    const { save } = useHistoryStore.getState();
    const { thinkingModel } = getModel();
    // 设置状态为写作中
    setStatus(t("research.common.writing"));
    // 初始化报告状态
    updateFinalReport("");
    setTitle("");
    setSources([]);

    // 提取所有任务的学习内容
    const learnings = tasks.map((item) => item.learning);
    // 去重并合并所有搜索来源
    const sources: Source[] = unique(
      flat(tasks.map((item) => item.sources || [])),
      (item) => item.url
    );
    // 去重并合并所有图片来源
    const images: ImageSource[] = unique(
      flat(tasks.map((item) => item.images || [])),
      (item) => item.url
    );
    // 判断是否启用图片引用和文本引用
    const enableCitationImage = images.length > 0 && citationImage === "enable";
    const enableReferences = sources.length > 0 && references === "enable";

    const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
    // 获取选中的自定义格式（如果有）
    const selectedCustomFormat = reportType === 'custom' && selectedCustomFormatId
      ? customFormats.find(format => format.id === selectedCustomFormatId)
      : undefined;

    // 构建最终报告prompt
    let finalPrompt;
    if (reportType === 'custom' && !selectedCustomFormatId && customPrompt) {
      // 使用直接输入的自定义prompt
      finalPrompt = buildFinalReportPrompt(
        reportType,
        {
          learnings: learnings.join('\n\n'),
          sources: enableReferences ? sources.map((item) => pick(item, ["title", "url"])) : [],
          images: enableCitationImage ? images : [],
          plan: reportPlan,
          requirement
        },
        {
          id: 'temp-custom',
          name: 'Custom Format',
          description: 'User-defined format',
          prompt: customPrompt,
          planWeight: 'weak',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        undefined,
        enableReferences,
        enableCitationImage
      );
    } else if (reportType === 'custom' && selectedCustomFormat) {
      // 使用保存的自定义格式
      finalPrompt = buildFinalReportPrompt(
        reportType,
        {
          learnings: learnings.join('\n\n'),
          sources: enableReferences ? sources.map((item) => pick(item, ["title", "url"])) : [],
          images: enableCitationImage ? images : [],
          plan: reportPlan,
          requirement
        },
        selectedCustomFormat,
        undefined,
        enableReferences,
        enableCitationImage
      );
    } else {
      // 使用预定义格式
      finalPrompt = buildFinalReportPrompt(
        reportType,
        {
          learnings: learnings.join('\n\n'),
          sources: enableReferences ? sources.map((item) => pick(item, ["title", "url"])) : [],
          images: enableCitationImage ? images : [],
          plan: reportPlan,
          requirement
        },
        undefined,
        undefined,
        enableReferences,
        enableCitationImage
      );
    }

    // 使用AI生成最终报告
    const result = streamText({
      model: await createModelProvider(thinkingModel),
      system: [getSystemPrompt(), outputGuidelinesPrompt].join("\n\n"),
      prompt: [
        finalPrompt,
        getResponseLanguagePrompt(),
      ].join("\n\n"),
      experimental_transform: smoothTextStream(smoothTextStreamType),
      onError: handleError,
    });

    let content = "";   // 存储报告内容
    let reasoning = ""; // 存储推理过程

    // 处理流式响应
    for await (const part of result.fullStream) {
      if (part.type === "text-delta") {
        thinkTagStreamProcessor.processChunk(
          part.textDelta,
          (data) => {
            content += data;
            updateFinalReport(content);
          },
          (data) => {
            reasoning += data;
          }
        );
      } else if (part.type === "reasoning") {
        reasoning += part.textDelta;
      }
    }
    if (reasoning) console.log(reasoning);

    // 如果有引用来源，添加到报告末尾
    if (sources.length > 0) {
      content +=
        "\n\n---\n\n## References\n\n" +
        sources
          .map(
            (item, idx) =>
              `[${idx + 1}]: ${item.url}${
                item.title ? ` "${item.title.replaceAll('"', " ")}"` : ""
              }`
          )
          .join("\n");
      updateFinalReport(content);
    }

    // 如果生成了内容，保存报告并设置相关信息
    if (content.length > 0) {
      // 从内容第一行提取标题
      const title = (content || "")
        .split("\n")[0]
        .replaceAll("#", "")
        .replaceAll("*", "")
        .trim();
      setTitle(title);
      setSources(sources);
      // 保存到历史记录并获取ID
      const id = save(taskStore.backup());
      setId(id);
      return content;
    } else {
      return "";
    }
  }

  /**
   * 深度研究主函数
   * 基于报告计划生成搜索查询并执行搜索任务
   */
  async function deepResearch() {
    // 获取报告计划和搜索模式
    const { reportPlan } = useTaskStore.getState();
    const { searchMode } = useSettingStore.getState();
    const { thinkingModel } = getModel();
    setStatus(t("research.common.thinking"));
    try {
      const thinkTagStreamProcessor = new ThinkTagStreamProcessor();
      // 使用AI基于报告计划生成搜索查询
      const result = streamText({
        model: await createModelProvider(thinkingModel),
        system: getSystemPrompt(),
        prompt: [
          generateSerpQueriesPrompt(reportPlan, searchMode),
          getResponseLanguagePrompt(),
        ].join("\n\n"),
        experimental_transform: smoothTextStream(smoothTextStreamType),
        onError: handleError,
      });

      // 获取搜索查询的JSON模式验证器
      const querySchema = getSERPQuerySchema();
      let content = "";           // 存储AI生成的内容
      let reasoning = "";         // 存储推理过程
      let queries: SearchTask[] = []; // 存储解析出的搜索查询

      // 处理流式文本响应
      for await (const textPart of result.textStream) {
        thinkTagStreamProcessor.processChunk(
          textPart,
          (text) => {
            content += text;
            // 尝试解析JSON格式的查询列表
            const data: PartialJson = parsePartialJson(
              removeJsonMarkdown(content)
            );
            // 验证解析结果是否符合查询模式
            if (querySchema.safeParse(data.value)) {
              if (
                data.state === "repaired-parse" ||
                data.state === "successful-parse"
              ) {
                if (data.value) {
                  // 将解析出的查询转换为SearchTask格式
                  queries = data.value.map(
                    (item: { query: string; language: "chinese" | "english"; groupId?: string; researchGoal: string }) => ({
                      state: "unprocessed",
                      learning: "",
                      sources: [],
                      images: [],
                      ...pick(item, ["query", "language", "groupId", "researchGoal"]),
                    })
                  );
                  // 更新任务存储
                  taskStore.update(queries);
                }
              }
            }
          },
          (text) => {
            reasoning += text;
          }
        );
      }
      if (reasoning) console.log(reasoning);
      // 执行生成的搜索任务
      await runSearchTask(queries);
    } catch (err) {
      console.error(err);
    }
  }

  /**
   * 对抗机制优化报告计划
   * 通过架构师-批判者双角色对抗，迭代优化计划质量
   */
  async function runAdversarialPlanOptimization(maxRounds: number, mode: 'fixed' | 'auto') {
    const { query, initAdversarialPlan, setAdversarialPlanRunning, addAdversarialRound, updateAdversarialPersona, updateAdversarialConversationText, finishAdversarialPlan } = useTaskStore.getState();
    
    // 初始化对抗计划状态
    initAdversarialPlan(maxRounds, mode);
    setAdversarialPlanRunning(true);
    
    try {
      let conversationText = "";
      let currentPersona: '架构师' | '批判者';
      let roundNumber = 1;
      let finalPlan: string = "";
      
      // 获取思考模型
      const { thinkingModel } = getModel();
      
      while (roundNumber <= maxRounds) {
        // 根据轮次确定角色：奇数轮=架构师，偶数轮=批判者
        currentPersona = (roundNumber % 2 === 1) ? '架构师' : '批判者';
        setStatus(`Round ${roundNumber}: ${currentPersona === '架构师' ? 'Architect' : 'Critic'} thinking...`);
        updateAdversarialPersona(currentPersona);
        
        // 准备 prompt
        const prompt = getReportPlanPrompt({
          nextPersona: currentPersona,
          query,
          conversationText,
          isFirstTurn: roundNumber === 1,
          mode,
          maxRounds,
          currentRound: roundNumber
        });
        
        // 调用AI生成响应
        const result = streamText({
          model: await createModelProvider(thinkingModel),
          system: getSystemPrompt(),
          prompt: [prompt, getResponseLanguagePrompt()].join("\n\n"),
          experimental_transform: smoothTextStream(smoothTextStreamType),
          onError: handleError,
        });
        
        let content = "";
        
        // 处理流式响应
        for await (const part of result.fullStream) {
          if (part.type === "text-delta") {
            content += part.textDelta;
            // 实时更新对话文本
            updateAdversarialConversationText(conversationText + `\n\n**Round ${roundNumber} - ${currentPersona}:**\n${content}`);
          }
        }
        
        try {
          // 解析JSON响应
          const cleanedContent = removeJsonMarkdown(content);
          console.log(`Round ${roundNumber} Raw Content:`, content);
          console.log(`Round ${roundNumber} Cleaned Content:`, cleanedContent);
          
          const parsedResponse = JSON.parse(cleanedContent);
          console.log(`Round ${roundNumber} Parsed Response:`, parsedResponse);
          
          // 验证响应格式
          const { thought, action, plan, critique, finish_reason } = parsedResponse;
          
          // 添加调试日志
          console.log(`Round ${roundNumber} Debug:`, {
            persona: currentPersona,
            action,
            critiqueType: typeof critique,
            critiqueValue: critique,
            planType: typeof plan,
            thoughtType: typeof thought
          });

          // 添加严格的响应验证
          if (!thought || typeof thought !== 'string' || thought.trim().length === 0) {
            throw new Error(`Invalid thought field: expected non-empty string, got ${typeof thought} with value: ${thought}`);
          }

          if (!action || !['propose_plan', 'critique_plan', 'finish_discussion'].includes(action)) {
            throw new Error(`Invalid action field: expected valid action (propose_plan/critique_plan/finish_discussion), got: ${action}`);
          }

          // 验证plan字段（如果存在，必须是数组且包含正确结构）
          if (plan !== undefined) {
            if (!Array.isArray(plan)) {
              throw new Error(`Invalid plan field: expected array or undefined, got ${typeof plan}`);
            }
            // 验证plan数组中的每个元素
            for (let i = 0; i < plan.length; i++) {
              const item = plan[i];
              if (!item || typeof item !== 'object') {
                throw new Error(`Invalid plan item at index ${i}: expected object, got ${typeof item}`);
              }
              if (!item.section_title || typeof item.section_title !== 'string') {
                throw new Error(`Invalid plan item at index ${i}: missing or invalid section_title`);
              }
              if (!item.summary || typeof item.summary !== 'string') {
                throw new Error(`Invalid plan item at index ${i}: missing or invalid summary`);
              }
            }
          }

          // 验证critique字段（如果存在，必须是字符串或对象）
          if (critique !== undefined) {
            if (typeof critique !== 'string' && (typeof critique !== 'object' || critique === null)) {
              throw new Error(`Invalid critique field: expected string, object, or undefined, got ${typeof critique}`);
            }
          }

          // 验证critique字段是否正确填写
          if (action === 'critique_plan') {
            if (!critique) {
              console.warn(`Round ${roundNumber}: Critic used critique_plan action but provided empty critique field`);
              throw new Error('Critic must provide specific feedback in the critique field when using critique_plan action');
            }
            
            // 支持字符串或结构化对象格式
            if (typeof critique === 'string') {
              if (critique.trim().length === 0) {
                console.warn(`Round ${roundNumber}: Critic provided empty string critique`);
                throw new Error('Critic must provide specific feedback in the critique field when using critique_plan action');
              }
            } else if (typeof critique === 'object' && critique !== null) {
              // 验证结构化critique至少包含一些有效内容
              // 支持两种格式：
              // 1. 键值对格式，值为字符串：{"topic_relevance": "some feedback", ...}
              // 2. 键值对格式，值为数组：{"topic_relevance": ["feedback1", "feedback2"], ...}
              const hasValidContent = Object.values(critique).some(value => {
                if (typeof value === 'string') {
                  // 字符串格式的批判内容
                  return value.trim().length > 0;
                } else if (Array.isArray(value)) {
                  // 数组格式的批判内容
                  return value.length > 0 && value.some(item => 
                    typeof item === 'string' && item.trim().length > 0
                  );
                }
                return false;
              });
              if (!hasValidContent) {
                console.warn(`Round ${roundNumber}: Critic provided structured critique but with no valid content`);
                console.warn(`Critique object:`, critique);
                console.warn(`Critique values types:`, Object.values(critique).map(v => typeof v));
                throw new Error('Critic must provide specific feedback in the critique field when using critique_plan action');
              }
            } else {
              console.warn(`Round ${roundNumber}: Critic provided invalid critique type:`, typeof critique);
              throw new Error('Critic must provide specific feedback in the critique field when using critique_plan action');
            }
          }
          
          // 创建轮次记录
          const roundRecord: AdversarialRound = {
            roundNumber,
            persona: currentPersona,
            thought,
            action,
            plan,
            critique,
            finish_reason,
            timestamp: Date.now()
          };
          
          // 添加轮次记录
          addAdversarialRound(roundRecord);
          
          // 更新对话文本
          conversationText += `\n\n**Round ${roundNumber} - ${currentPersona}:**\n${thought}\nAction: ${action}`;
          if (plan) {
            const planText = plan.map((item: {section_title: string; summary: string}) => `${item.section_title}: ${item.summary}`).join('\n');
            conversationText += `\nPlan:\n${planText}`;
          }
          if (critique) {
            if (typeof critique === 'string') {
              conversationText += `\nCritique: ${critique}`;
            } else if (typeof critique === 'object' && critique !== null) {
              conversationText += `\nCritique:`;
              Object.entries(critique).forEach(([key, value]) => {
                if (typeof value === 'string' && value.trim().length > 0) {
                  // 字符串值的批判内容
                  conversationText += `\n  ${key}: ${value}`;
                } else if (Array.isArray(value) && value.length > 0) {
                  // 数组值的批判内容
                  conversationText += `\n  ${key}: ${value.join('; ')}`;
                }
              });
            }
          }
          if (finish_reason) {
            conversationText += `\nFinish Reason: ${finish_reason}`;
          }
          
          updateAdversarialConversationText(conversationText);

          // 添加调试日志
          console.log(`Round ${roundNumber} End Check:`, {
            mode,
            action,
            currentPersona,
            maxRounds,
            hasPlan: !!plan,
            shouldEnd: action === 'finish_discussion' || (mode === 'fixed' && roundNumber === maxRounds && currentPersona === '批判者')
          });

          // 检查是否结束讨论
          if (action === 'finish_discussion' && plan) {
            console.log(`Ending due to finish_discussion action`);
            // 将计划转换为标准格式
            const planSections = plan.map((item: {section_title: string; summary: string}) => `## ${item.section_title}\n${item.summary}`).join('\n\n');
            finalPlan = planSections;
            break;
          }

          // 在固定轮数模式下，验证最后一轮批判者必须使用finish_discussion
          if (mode === 'fixed' && roundNumber === maxRounds && currentPersona === '批判者') {
            if (action !== 'finish_discussion') {
              console.error(`Fixed mode final round: Critic used '${action}' instead of required 'finish_discussion'`);
              // 如果批判者没有使用finish_discussion，强制要求重试
              throw new Error('In fixed mode final round, Critic MUST use finish_discussion action to conclude the optimization');
            }
            if (!plan) {
              console.error(`Fixed mode final round: Critic used finish_discussion but provided no plan`);
              throw new Error('Critic must provide the final plan when using finish_discussion action');
            }
            console.log(`Ending due to fixed mode final round with finish_discussion`);
            const planSections = plan.map((item: {section_title: string; summary: string}) => `## ${item.section_title}\n${item.summary}`).join('\n\n');
            finalPlan = planSections;
            break;
          }
          
          // 进入下一轮（角色会在while循环开始时根据轮次自动确定）
          roundNumber++;
          
        } catch (parseError) {
          console.error(`Failed to parse AI response for round ${roundNumber}:`, {
            error: parseError,
            rawContent: content,
            cleanedContent: removeJsonMarkdown(content),
            persona: currentPersona,
            roundNumber
          });
          
          // 如果是解析错误且还没超过最大轮数，尝试重试
          if (roundNumber <= maxRounds) {
            console.log(`Retrying round ${roundNumber} due to parsing error`);
            // 角色会在while循环开始时根据轮次自动确定，这里不需要手动设置
            continue;
          }
          
          handleError(`无法解析AI响应格式 (Round ${roundNumber}): ${parseError instanceof Error ? parseError.message : String(parseError)}`);
          break;
        }
      }
      
      // 完成对抗优化，更新最终计划
      if (finalPlan) {
        finishAdversarialPlan(finalPlan);
        setStatus("Adversarial plan optimization completed");
      } else {
        handleError("Failed to generate final plan through adversarial optimization");
      }
      
    } catch (error) {
      console.error("Adversarial optimization error:", error);
      handleError("对抗优化过程出现错误");
    } finally {
      setAdversarialPlanRunning(false);
    }
  }

  // 返回Hook提供的所有功能函数
  return {
    status,              // 当前状态
    deepResearch,        // 深度研究主函数
    askQuestions,        // 生成研究问题
    writeReportPlan,     // 编写报告计划
    runSearchTask,       // 执行搜索任务
    reviewSearchResult,  // 审查搜索结果
    writeFinalReport,    // 编写最终报告
    runAdversarialPlanOptimization, // 对抗机制优化报告计划
  };
}

// 导出深度研究Hook
export default useDeepResearch;
