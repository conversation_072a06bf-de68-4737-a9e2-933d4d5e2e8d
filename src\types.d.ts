interface Resource {
  id: string;
  name: string;
  type: string;
  size: number;
  status: "unprocessed" | "processing" | "completed" | "failed";
}

interface FileMeta {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

interface Knowledge {
  id: string;
  title: string;
  content: string;
  type: "file" | "url" | "knowledge";
  fileMeta?: FileMeta;
  url?: string;
  createdAt: number;
  updatedAt: number;
}

interface ImageSource {
  url: string;
  description?: string;
  chineseDescription?: string; // 中文描述，用于在最终报告中显示图片说明
}

interface Source {
  title?: string;
  content?: string;
  url: string;
  images?: ImageSource[];
}

// 搜索模式类型定义
type SearchMode = "bilingual" | "chinese" | "english";

interface SearchTask {
  state: "unprocessed" | "processing" | "completed" | "failed";
  query: string;           // 实际查询词（中文或英文）
  language: "chinese" | "english";  // 语言标识
  groupId?: string;        // 可选：用于关联中英文任务对（双语模式下使用）
  researchGoal: string;
  learning: string;        // 搜索结果内容
  sources: Source[];       // 来源链接
  images: ImageSource[];   // 图片来源
  // Auto research related fields
  roundNumber?: number;    // 所属轮次
  taskType?: "search" | "reflection" | "evaluation" | "chapter_start" | "agent_discussion" | "chapter_completed";  // 任务类型
}

interface PartialJson {
  value: JSONValue | undefined;
  state:
    | "undefined-input"
    | "successful-parse"
    | "repaired-parse"
    | "failed-parse";
}

interface WebSearchResult {
  content: string;
  url: string;
  title?: string;
}
