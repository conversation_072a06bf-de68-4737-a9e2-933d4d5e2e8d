import { 
  finalReportCitationImagePrompt 
} from '@/constants/prompts';
// Source 和 ImageSource 是全局类型，在 types.d.ts 中定义

// 生成第一章节预览报告的prompt（无参考来源版本）
export const generateFirstChapterPreviewPrompt = (
  chapterTitle: string,
  chapterGoal: string,
  collectedInfo: string
): string => {
  return `
基于以下信息，为"${chapterTitle}"章节写一份研究报告（仅正文内容，不包含参考来源）：

章节目标: ${chapterGoal}

收集的信息:
${collectedInfo}

要求：
1. 围绕章节目标组织内容，重点阐述背景信息和问题界定
2. 使用收集到的信息支撑观点
3. 结构清晰，逻辑连贯，使用markdown格式
4. 使用中文写作
5. 不需要包含参考来源和引用标记
6. 突出对其他章节研究有指导意义的背景信息

请生成章节报告的正文内容。
  `;
};

export const generateChapterReportPrompt = (
  chapterTitle: string,
  chapterGoal: string,
  collectedInfo: string,
  sources: Source[],
  images: ImageSource[],
  enableReferences: boolean = true,
  enableImages: boolean = true,
  globalCitationsMap?: Map<string, number> // 新增参数：全局编号映射
): string => {
  // 构建结构化的sources部分（使用全局编号）
  const sourcesSection = enableReferences && sources.length > 0
    ? sources.map((source) => {
        const globalIndex = globalCitationsMap?.get(source.url) || 1;
        return `<source index="${globalIndex}" url="${source.url}">\n${source.title}\n</source>`;
      }).join('\n')
    : '';
  
  // 获取全局编号范围，用于指导AI
  const globalIndices = globalCitationsMap ? Array.from(globalCitationsMap.values()).sort((a, b) => a - b) : [];
  const minIndex = globalIndices.length > 0 ? Math.min(...globalIndices) : 1;
  const maxIndex = globalIndices.length > 0 ? Math.max(...globalIndices) : 1;
  
  const imageSection = enableImages && images.length > 0 
    ? `\n\n**可用图片资源:**\n${images.map(img => `- ![${img.description || ''}](${img.url})${img.chineseDescription ? ` (${img.chineseDescription})` : ''}`).join('\n')}\n\n${finalReportCitationImagePrompt}` 
    : '';
  
  // 自定义引用规则，明确指出编号范围
  const customCitationRules = `Citation Rules:

- IMPORTANT: The sources for this chapter are numbered from [${minIndex}] to [${maxIndex}]
- Use the EXACT numbers provided in the <source index="number"> tags
- DO NOT start from [1] unless the source index actually shows "1"
- The collected information above already contains citations with correct numbers - keep using those same numbers
- When citing sources in the text, use the format [number] where number matches the source index
- If a statement comes from multiple sources, list them like [${minIndex}][${minIndex + 1}]
- CRITICAL: At the end of the report, you MUST add markdown reference definitions for ALL cited sources
- These definitions enable hover links on inline citations`;
  
  const referencesSection = enableReferences && sources.length > 0 
    ? `\n\n**参考文献要求**:\n- 在报告末尾添加"## 参考来源"部分\n- 对于每个引用，先显示为可点击的列表项，再紧跟着添加其Markdown定义\n- 格式必须为:\n  [编号]. [标题](URL)\n  [编号]: URL "标题"\n- 每个引用独占一组，不要添加额外的符号或格式\n- 编号必须与正文中的引用编号一致（从[${minIndex}]到[${maxIndex}]）\n- 示例：\n  [${minIndex}]. [示例标题](https://example.com)\n  [${minIndex}]: https://example.com "示例标题"\n  \n  [${minIndex + 1}]. [另一个标题](https://another.com)\n  [${minIndex + 1}]: https://another.com "另一个标题"` 
    : '';

  return `
基于以下信息，为"${chapterTitle}"章节写一份详细的研究报告：

章节目标: ${chapterGoal}

收集的信息（注意：以下信息中已包含正确的引用编号，请保持使用相同的编号）:
${collectedInfo}

${enableReferences && sources.length > 0 ? `参考来源（请在报告中引用这些来源）:
<SOURCES>
${sourcesSection}
</SOURCES>` : ''}

要求：
1. 围绕章节目标组织内容
2. 使用收集到的信息支撑观点
3. 结构清晰，逻辑连贯，使用markdown格式，章节题目为##[章节题目]， 小标题为###[小标题]
4. 使用中文写作
5. 包含必要的引用说明${enableReferences ? '和引用列表' : ''}${enableImages ? '和相关图片' : ''}
${enableReferences ? `6. 引用编号范围：本章节的引用编号从[${minIndex}]到[${maxIndex}]，不要使用这个范围之外的编号` : ''}

${enableReferences ? customCitationRules : ''}${imageSection}${referencesSection}

请生成一份完整内容丰富的章节报告。
  `;
};
