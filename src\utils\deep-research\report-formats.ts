/**
 * Report format configuration and prompt building utilities
 * Implements layered prompt architecture for multiple report formats
 */

import { ReportType, PlanWeight, CustomReportFormat } from '@/types/report';
import { finalReportReferencesPrompt, finalReportCitationImagePrompt } from '@/constants/prompts';

/**
 * Core data layer - always included in all report formats
 * Contains the essential research data collected during the research process
 */
function buildCoreDataSection(
  learnings: string,
  sources: any[],
  images: any[],
  enableReferences: boolean,
  enableCitationImage: boolean
): string {
  // Format learnings for references
  const formattedLearnings = enableReferences
    ? learnings.split('\n\n').map(learning => `<learning>\n${learning}\n</learning>`).join('\n')
    : learnings;

  // Format sources for references
  const formattedSources = enableReferences
    ? sources.map((item, idx) => `<source index="${idx + 1}" url="${item.url}">\n${item.title}\n</source>`).join('\n')
    : JSON.stringify(sources, null, 2);

  // Format images for citation
  const formattedImages = enableCitationImage
    ? images.map((source, idx) => `${idx + 1}. ![${source.description}](${source.url})\n   (图片说明：${source.chineseDescription || source.description})`).join('\n')
    : JSON.stringify(images, null, 2);

  return `
<RESEARCH_DATA>
<LEARNINGS>
${formattedLearnings}
</LEARNINGS>

<SOURCES>
${formattedSources}
</SOURCES>

<IMAGES>
${formattedImages}
</IMAGES>
</RESEARCH_DATA>`;
}

/**
 * Plan reference layer - configurable based on report type
 * Controls how much influence the original research plan has on the final report
 */
const planSections = {
  strong: `<REPORT_STRUCTURE>
Strictly follow this confirmed report plan structure:
{plan}
</REPORT_STRUCTURE>`,
  
  weak: `<STRUCTURE_REFERENCE>
You may reference this original plan if helpful, but prioritize the format requirements below:
{plan}
</STRUCTURE_REFERENCE>`,
  
  none: ''
};

/**
 * Format instruction layer - defines the specific format for each report type
 * This is where the main differentiation between report types happens
 */
const formatInstructions = {
  standard: `Write a comprehensive research report based on the report plan by thoroughly analyzing and integrating all the learnings from research.
Pay attention to consolidating similar information, avoiding repetition, and ensuring coherence and logical flow of the report.
Make it as detailed as possible, aim for 5 pages or more, the more the better.`,

  'collateral-analysis': `## **Core Structure**
**Title Format**: "Research/Analysis on [Event/Policy/Topic]" or "Impact Analysis of [Policy/Event] from [Perspective]"

**Introduction**:
- Lead with recent key policy/market event
- State analysis objective and scope

## **Main Body (3-4 Sections)**
### **Section 1: Background Overview**
- **What**: Fundamental situation, policy terms, key elements
- **Format**: Detailed descriptions with diagrams where helpful
- **Focus**: Set stage for analysis

### **Section 2: Causal Analysis**
- **Why**: Underlying reasons, policy context, problem identification
- **Include**: International comparisons (US/Europe), regulatory background
- **Purpose**: Provide broader context

### **Section 3: Impact Assessment**
- **Effects**: Market/institutional/operational impacts
- **Structure**: Bulleted positive/negative effects on liquidity, risk, participant behavior
- **Support**: Market data, expert opinions

### **Section 4: Conclusions & Recommendations**
- **Next Steps**: Strategic directions, actionable recommendations
- **Perspective**: Align with CHINA CENTRAL DEPOSITORY & CLEARING CO., LTD's role
- **Highlight**: Institution's unique value in regulation implementation and risk mitigation

## **Appendices**
- Complex comparative data in tables
- Detailed regulatory excerpts
- Supporting technical information

**Key Requirement**: Maintain analytical depth while showcasing the publishing institution's expertise and market position.`,

  'test': `Write a comprehensive research report based on the report plan by thoroughly analyzing and integrating all the learnings from research.
Pay attention to consolidating similar information, avoiding repetition, and ensuring coherence and logical flow of the report.
Make it as detailed as possible, aim for 5 pages or more, the more the better.`,
  custom: `{customPrompt}`
};

/**
 * Build the final report prompt by combining all layers
 */
export function buildFinalReportPrompt(
  reportType: ReportType,
  data: {
    learnings: string;
    sources: any[];
    images: any[];
    plan: string;
    requirement: string;
  },
  customFormat?: CustomReportFormat,
  planWeight?: PlanWeight,
  enableReferences: boolean = false,
  enableCitationImage: boolean = false
): string {
  // Determine plan weight based on report type or custom format
  const weight = planWeight || getDefaultPlanWeight(reportType, customFormat);
  
  // Get format instructions
  let formatInstruction = formatInstructions[reportType];
  if (reportType === 'custom' && customFormat) {
    formatInstruction = formatInstruction.replace('{customPrompt}', customFormat.prompt);
  }
  
  // Build the complete prompt by combining all layers
  const sections = [
    // Core data layer (always included)
    buildCoreDataSection(
      data.learnings,
      data.sources,
      data.images,
      enableReferences,
      enableCitationImage
    ),

    // Plan reference layer (conditional)
    planSections[weight]?.replace('{plan}', data.plan) || '',

    // User requirements (if provided)
    data.requirement ? `<USER_REQUIREMENTS>\n${data.requirement}\n</USER_REQUIREMENTS>` : '',

    // Format instruction layer
    `<FORMAT_INSTRUCTIONS>\n${formatInstruction}\n</FORMAT_INSTRUCTIONS>`,

    // Citation and reference instructions (if enabled)
    enableCitationImage ? `\n**Including meaningful images from the previous research in the report is very helpful.**\n\n${finalReportCitationImagePrompt}` : '',

    enableReferences ? `\n\n${finalReportReferencesPrompt}` : '',

    // Final instruction
    `**Respond only the final report content, and no additional text before or after.**`
  ];

  return sections.filter(Boolean).join('\n\n');
}

/**
 * Get default plan weight for a report type
 */
function getDefaultPlanWeight(reportType: ReportType, customFormat?: CustomReportFormat): PlanWeight {
  if (reportType === 'custom' && customFormat) {
    return customFormat.planWeight;
  }
  
  const weights: Record<ReportType, PlanWeight> = {
    standard: 'strong',
    'collateral-analysis': 'weak',
    test: 'none',
    custom: 'weak'
  };
  
  return weights[reportType];
}

/**
 * Validate custom format prompt
 */
export function validateCustomPrompt(prompt: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!prompt || prompt.trim().length === 0) {
    errors.push('Prompt cannot be empty');
  }
  
  if (prompt.length < 50) {
    errors.push('Prompt should be at least 50 characters long for meaningful formatting');
  }
  
  if (prompt.length > 5000) {
    errors.push('Prompt is too long (maximum 5000 characters)');
  }
  
  // Check for potentially problematic content
  const problematicPatterns = [
    /ignore\s+all\s+previous\s+instructions/i,
    /forget\s+everything/i,
    /system\s+prompt/i
  ];
  
  for (const pattern of problematicPatterns) {
    if (pattern.test(prompt)) {
      errors.push('Prompt contains potentially problematic instructions');
      break;
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get prompt writing guidelines for users
 */
export function getPromptGuidelines(): string {
  return `# Custom Report Format Guidelines

## Best Practices for Writing Effective Prompts

### 1. Structure Your Prompt Clearly
- Use clear headings and sections
- Define the expected output format
- Specify the order of information

### 2. Be Specific About Requirements
- Mention desired length or depth
- Specify tone and style (formal, casual, technical)
- Include any special formatting needs

### 3. Provide Context
- Explain the purpose of the report
- Define the target audience
- Mention any industry-specific requirements

### 4. Use Examples When Helpful
- Show the desired format structure
- Provide sample section headings
- Demonstrate the expected style

### 5. Avoid Conflicting Instructions
- Don't contradict the core research data
- Keep instructions focused and coherent
- Avoid overly complex nested requirements

## Example Template:
\`\`\`
Write a [TYPE] report with the following structure:

## Section 1: [Name]
- [Specific requirements]
- [Expected content type]

## Section 2: [Name]
- [Specific requirements]
- [Expected content type]

Use [TONE] language and focus on [AUDIENCE].
Target length: [LENGTH] pages.
\`\`\``;
}
