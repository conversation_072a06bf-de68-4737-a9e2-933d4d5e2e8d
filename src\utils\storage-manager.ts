/**
 * 存储管理工具
 * 提供统一的数据清除和存储信息查询功能
 */

import { researchStore } from './storage';
import { useTaskStore } from '@/store/task';
import { useSettingStore } from '@/store/setting';
import { useHistoryStore } from '@/store/history';
import { useKnowledgeStore } from '@/store/knowledge';

export interface StorageInfo {
  localStorage: {
    research: number;
    setting: number;
    total: number;
  };
  indexedDB: {
    historyStore: number;
    knowledgeStore: number;
    total: number;
  };
  totalSize: number;
}

export interface ClearOptions {
  research?: boolean;
  settings?: boolean;
  history?: boolean;
  knowledge?: boolean;
}

class StorageManager {
  /**
   * 获取存储使用情况
   */
  async getStorageInfo(): Promise<StorageInfo> {
    const info: StorageInfo = {
      localStorage: { research: 0, setting: 0, total: 0 },
      indexedDB: { historyStore: 0, knowledgeStore: 0, total: 0 },
      totalSize: 0
    };

    try {
      // 计算 localStorage 大小
      const researchData = localStorage.getItem('research');
      const settingData = localStorage.getItem('setting');
      
      info.localStorage.research = researchData ? new Blob([researchData]).size : 0;
      info.localStorage.setting = settingData ? new Blob([settingData]).size : 0;
      info.localStorage.total = info.localStorage.research + info.localStorage.setting;

      // 计算 IndexedDB 大小（近似）
      try {
        const historyKeys = await researchStore.keys();
        const knowledgeKeys = historyKeys.filter(key => key.includes('knowledgeStore') || key.includes('historyStore'));
        
        for (const key of knowledgeKeys) {
          const data = await researchStore.getItem(key);
          if (data) {
            const size = new Blob([JSON.stringify(data)]).size;
            if (key.includes('historyStore')) {
              info.indexedDB.historyStore += size;
            } else if (key.includes('knowledgeStore')) {
              info.indexedDB.knowledgeStore += size;
            }
          }
        }
      } catch (e) {
        console.warn('Unable to calculate IndexedDB size:', e);
      }

      info.indexedDB.total = info.indexedDB.historyStore + info.indexedDB.knowledgeStore;
      info.totalSize = info.localStorage.total + info.indexedDB.total;

    } catch (error) {
      console.error('Failed to get storage info:', error);
    }

    return info;
  }

  /**
   * 清除指定类型的数据
   */
  async clearData(options: ClearOptions = {}): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];
    let success = true;

    try {
      // 清除研究数据 (localStorage)
      if (options.research) {
        try {
          // 获取任务存储实例
          const taskStore = useTaskStore.getState();
          
          // 专门重置对抗性计划状态
          if (taskStore.resetAdversarialPlan) {
            taskStore.resetAdversarialPlan();
          }
          
          // 重置任务存储到默认状态
          taskStore.reset();
          
          // 强制清除 localStorage 中的数据
          localStorage.removeItem('research');
          
          console.log('✅ 已清除研究数据（包括章节研究和对抗性计划状态）');
        } catch (e) {
          errors.push(`清除研究数据失败: ${e}`);
          success = false;
        }
      }

      // 清除设置数据 (localStorage)
      if (options.settings) {
        try {
          const settingStore = useSettingStore.getState();
          settingStore.reset();
          
          // 强制清除 localStorage 中的数据
          localStorage.removeItem('setting');
          
          console.log('✅ 已清除设置数据');
        } catch (e) {
          errors.push(`清除设置数据失败: ${e}`);
          success = false;
        }
      }

      // 清除历史记录 (IndexedDB)
      if (options.history) {
        try {
          await researchStore.removeItem('historyStore');
          
          // 重置内存中的历史存储
          const historyStore = useHistoryStore.getState();
          // 手动清空历史数组
          historyStore.history.length = 0;
          
          console.log('✅ 已清除历史记录');
        } catch (e) {
          errors.push(`清除历史记录失败: ${e}`);
          success = false;
        }
      }

      // 清除知识库 (IndexedDB)
      if (options.knowledge) {
        try {
          await researchStore.removeItem('knowledgeStore');
          
          // 重置内存中的知识库存储
          const knowledgeStore = useKnowledgeStore.getState();
          // 手动清空知识库数组
          knowledgeStore.knowledges.length = 0;
          
          console.log('✅ 已清除知识库数据');
        } catch (e) {
          errors.push(`清除知识库数据失败: ${e}`);
          success = false;
        }
      }

    } catch (error) {
      errors.push(`清除数据过程中发生未知错误: ${error}`);
      success = false;
    }

    return { success, errors };
  }

  /**
   * 清除所有数据
   */
  async clearAllData(): Promise<{ success: boolean; errors: string[] }> {
    return this.clearData({
      research: true,
      settings: true,
      history: true,
      knowledge: true
    });
  }

  /**
   * 只清除当前研究数据，保留设置和历史
   */
  async clearCurrentResearch(): Promise<{ success: boolean; errors: string[] }> {
    return this.clearData({ research: true });
  }

  /**
   * 强制刷新页面以确保状态完全重置
   */
  forceRefresh(): void {
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  }

  /**
   * 格式化文件大小显示
   */
  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 检查是否有残留的章节研究数据
   */
  hasChapterResearchData(): boolean {
    return false; // 章节研究已移除
  }

  /**
   * 检查章节研究是否处于运行状态
   */
  isChapterResearchRunning(): boolean {
    return false; // 章节研究已移除
  }

  /**
   * 强制清除章节研究运行状态
   */
  forceStopChapterResearch(): boolean {
    console.log('✅ 章节研究已移除，无需停止');
    return true; // 总是返回成功
  }

  /**
   * 获取当前研究状态摘要
   */
  getResearchSummary(): {
    hasActiveResearch: boolean;
    hasChapterData: boolean;
    hasAdversarialData: boolean;
    taskCount: number;
    isChapterRunning: boolean;
  } {
    try {
      const taskStore = useTaskStore.getState();
      return {
        hasActiveResearch: !!(taskStore.title || taskStore.finalReport || taskStore.reportPlan),
        hasChapterData: this.hasChapterResearchData(),
        hasAdversarialData: !!(taskStore.adversarialPlan && taskStore.adversarialPlan.rounds.length > 0),
        taskCount: taskStore.tasks.length,
        isChapterRunning: this.isChapterResearchRunning()
      };
    } catch {
      return {
        hasActiveResearch: false,
        hasChapterData: false,
        hasAdversarialData: false,
        taskCount: 0,
        isChapterRunning: false
      };
    }
  }
}

// 导出单例实例
export const storageManager = new StorageManager();

// 导出类型
export type { StorageManager };