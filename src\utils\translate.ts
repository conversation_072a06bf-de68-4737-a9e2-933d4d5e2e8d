import { generateText } from "ai";
import { imageDescriptionTranslatePrompt } from "@/constants/prompts";

/**
 * 检测文本是否包含中文字符
 */
function containsChinese(text: string): boolean {
  return /[\u4e00-\u9fff]/.test(text);
}

/**
 * 批量翻译图片描述为中文
 * @param images 图片源数组
 * @param aiProvider AI提供者实例
 * @returns 翻译后的图片源数组
 */
export async function translateImageDescriptions(
  images: ImageSource[],
  aiProvider: any
): Promise<ImageSource[]> {
  if (!images || images.length === 0) {
    return images;
  }

  // 过滤需要翻译的描述
  const descriptionsToTranslate: string[] = [];
  const indexMap: number[] = []; // 记录需要翻译的描述在原数组中的索引

  images.forEach((image, index) => {
    if (image.description && image.description.trim() && !containsChinese(image.description)) {
      descriptionsToTranslate.push(image.description);
      indexMap.push(index);
    }
  });

  if (descriptionsToTranslate.length === 0) {
    return images; // 没有需要翻译的内容
  }

  try {
    // 构建翻译prompt
    const descriptionsText = descriptionsToTranslate
      .map((desc, idx) => `${idx + 1}. ${desc}`)
      .join("\n");
    
    const prompt = imageDescriptionTranslatePrompt.replace(
      "{descriptions}",
      descriptionsText
    );

    // 调用AI进行翻译
    const result = await generateText({
      model: aiProvider,
      prompt,
      temperature: 0.3, // 较低温度确保翻译准确性
    });

    // 解析翻译结果
    let translationResult;
    try {
      translationResult = JSON.parse(result.text);
    } catch (parseError) {
      console.warn("翻译结果JSON解析失败:", parseError);
      return images; // 解析失败时返回原始数据
    }

    const translations = translationResult.translations || [];

    if (!Array.isArray(translations)) {
      console.warn("翻译结果格式不正确");
      return images;
    }

    // 应用翻译结果
    const translatedImages = [...images];
    indexMap.forEach((originalIndex, translateIndex) => {
      if (translations[translateIndex] && typeof translations[translateIndex] === 'string') {
        const chineseDescription = translations[translateIndex].trim();
        translatedImages[originalIndex] = {
          ...translatedImages[originalIndex],
          description: chineseDescription, // 保持原有逻辑，用于悬停显示
          chineseDescription: chineseDescription // 新增字段，用于最终报告中的图片说明
        };
      }
    });

    console.log(`成功翻译 ${translations.length} 个图片描述`);
    return translatedImages;
  } catch (error) {
    console.warn("图片描述翻译失败，使用原始描述:", error);
    return images; // 翻译失败时返回原始数据
  }
}

/**
 * 单个图片描述翻译（备用方法）
 * @param description 原始描述
 * @param aiProvider AI提供者实例
 * @returns 翻译后的描述
 */
export async function translateSingleImageDescription(
  description: string,
  aiProvider: any
): Promise<string> {
  if (!description || !description.trim() || containsChinese(description)) {
    return description;
  }

  try {
    const prompt = `请将以下英文图片描述翻译为简洁的中文，保持原意不变：

"${description}"

只返回中文翻译，不要包含其他内容。`;

    const result = await generateText({
      model: aiProvider,
      prompt,
      temperature: 0.3,
    });

    return result.text.trim();
  } catch (error) {
    console.warn("单个图片描述翻译失败:", error);
    return description; // 翻译失败时返回原始描述
  }
}
