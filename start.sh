#!/bin/bash

# Deep Research 启动脚本

echo "🚀 启动 Deep Research..."

# 检查端口是否被占用
if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null ; then
    echo "❌ 端口 3001 已被占用"
    echo "占用进程："
    lsof -i :3001
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动应用并记录日志
echo "📝 启动应用，日志将保存到 logs/app.log"
nohup pnpm start > logs/app.log 2>&1 &

# 获取进程 PID
PID=$!
echo "✅ 应用已启动，PID: $PID"

# 等待几秒钟让应用完全启动
sleep 3

# 检查应用是否正常启动
if curl -s http://localhost:3001 > /dev/null; then
    echo "✅ 应用启动成功！"
    echo "🌐 访问地址: http://localhost:3001"
    echo "📝 查看日志: tail -f logs/app.log"
    echo "🔍 查看进程: ps aux | grep $PID"
else
    echo "❌ 应用启动失败，请检查日志："
    tail -n 20 logs/app.log
fi
